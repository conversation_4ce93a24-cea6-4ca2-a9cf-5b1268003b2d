"use client";

import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";

interface AddStoryButtonProps {
  onAddStory: () => void;
  milestoneColor?: string;
}

export default function AddStoryButton({ onAddStory, milestoneColor = "gray" }: AddStoryButtonProps) {
  // Get hover color based on milestone color
  const getHoverColor = () => {
    const colorMap: Record<string, string> = {
      red: "hover:border-red-400 hover:bg-red-50 dark:hover:bg-red-950/30",
      pink: "hover:border-pink-400 hover:bg-pink-50 dark:hover:bg-pink-950/30",
      rose: "hover:border-rose-400 hover:bg-rose-50 dark:hover:bg-rose-950/30",
      orange: "hover:border-orange-400 hover:bg-orange-50 dark:hover:bg-orange-950/30",
      amber: "hover:border-amber-400 hover:bg-amber-50 dark:hover:bg-amber-950/30",
      yellow: "hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-950/30",
      lime: "hover:border-lime-400 hover:bg-lime-50 dark:hover:bg-lime-950/30",
      green: "hover:border-green-400 hover:bg-green-50 dark:hover:bg-green-950/30",
      emerald: "hover:border-emerald-400 hover:bg-emerald-50 dark:hover:bg-emerald-950/30",
      teal: "hover:border-teal-400 hover:bg-teal-50 dark:hover:bg-teal-950/30",
      cyan: "hover:border-cyan-400 hover:bg-cyan-50 dark:hover:bg-cyan-950/30",
      sky: "hover:border-sky-400 hover:bg-sky-50 dark:hover:bg-sky-950/30",
      blue: "hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-950/30",
      indigo: "hover:border-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-950/30",
      violet: "hover:border-violet-400 hover:bg-violet-50 dark:hover:bg-violet-950/30",
      purple: "hover:border-purple-400 hover:bg-purple-50 dark:hover:bg-purple-950/30",
      fuchsia: "hover:border-fuchsia-400 hover:bg-fuchsia-50 dark:hover:bg-fuchsia-950/30",
    };
    
    return colorMap[milestoneColor] || "hover:border-primary hover:bg-primary/5";
  };
  
  return (
    <Button
      onClick={onAddStory}
      variant="ghost"
      className={`w-[280px] sm:w-[320px] md:w-[360px] lg:w-[400px] h-16 rounded-2xl border-dashed border-2 border-gray-300 dark:border-gray-600 bg-transparent flex items-center justify-center text-gray-600 dark:text-gray-400 ${getHoverColor()} transition-all duration-200 hover:scale-[1.02]`}
    >
      <PlusCircle className="h-5 w-5" />
    </Button>
  );
}
